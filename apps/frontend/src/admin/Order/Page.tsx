import { useTables } from "@/data/useTables";
import { useOrders } from "@/data/useOrders";
import { useProducts } from "@/data/useProducts";
import type { ColDef } from "ag-grid-community";
import { AgGridReact } from "ag-grid-react";
import type { Order, Product } from "@kassierer/shared/model";
import { PriceCentUtils } from "@/utils/priceCents";

import { ModuleRegistry, AllCommunityModule } from "ag-grid-community";

import "ag-grid-community/styles/ag-grid.css";
import "ag-grid-community/styles/ag-theme-quartz.css";
import { useMemo } from "react";

ModuleRegistry.registerModules([AllCommunityModule]);

function AdminOrderPage() {
  const tables = useTables(state => state.tables);
  const products = useProducts(state => state.products);
  const orders = useOrders((state) => state.orders);

  const productsMap = useMemo(() => {
    return products.reduce<Record<string, Product>>((pMap, product) => {
      pMap[product.id] = product;
      return pMap;
    }, {});
  }, [products]);

  // Helper function to calculate order total
  const calculateOrderTotal = (order: Order): number => {
    return order.content.reduce((total, contentItem) => {
      const product = productsMap[contentItem.productId];
      if (product) {
        return total + (product.priceCents * contentItem.items.length);
      }
      return total;
    }, 0);
  };

  // Helper function to get table name
  const getTableName = (tableId: string): string => {
    const table = tables.find(t => t.id === tableId);
    return table?.name || '-';
  };

  // Helper function to count total products in order
  const getTotalProductCount = (order: Order): number => {
    return order.content.reduce((total, contentItem) => {
      return total + contentItem.items.length;
    }, 0);
  };

  const columnDefs: ColDef<Order>[] = [
    {
      headerName: "Zeit",
      field: "createdAt",
      width: 150,
      valueFormatter: (params) => {
        return new Date(params.value).toLocaleTimeString();
      },
      sortable: true,
      initialSort: "desc",
      filter: true,
      editable: false,
    },
    {
      headerName: "Tisch",
      field: "tableId",
      width: 150,
      valueFormatter: (params) => {
        return getTableName(params.value);
      },
      sortable: true,
      filter: true,
      editable: false,
    },
    {
      headerName: "Anzahl Produkte",
      width: 180,
      valueGetter: (params) => {
        return getTotalProductCount(params.data!);
      },
      sortable: true,
      filter: "agNumberColumnFilter",
      editable: false,
    },
    {
      headerName: "Gesamtsumme",
      width: 150,
      valueGetter: (params) => {
        return calculateOrderTotal(params.data!);
      },
      valueFormatter: (params) => {
        return PriceCentUtils.toMoneyString(params.value);
      },
      sortable: true,
      filter: "agNumberColumnFilter",
      editable: false,
    },
  ];

  return (
    <div className="divide-y divide-gray-200 h-full overflow-hidden rounded-lg bg-white shadow-sm">
      <div className="px-4 py-5 sm:px-6">
        <div className="sm:flex sm:items-center">
          <div className="sm:flex-auto">
            <h1 className="text-base font-semibold text-gray-900">Bestellungen</h1>
          </div>
        </div>
      </div>
      <div>
        <div className="ag-theme-quartz">
          <AgGridReact
            rowData={orders}
            columnDefs={columnDefs}
            defaultColDef={{
              resizable: true,
              sortable: true,
              filter: true,
            }}
            pagination={true}
            paginationPageSize={10}
            domLayout="autoHeight"
            getRowId={(params) => params.data.id}
          />
        </div>
      </div>
    </div>
  );
}

export default AdminOrderPage;
