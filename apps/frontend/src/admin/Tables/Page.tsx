import { useState, useCallback } from "react";
import { useTables } from "@/data/useTables";
import { Button } from "primereact/button";
import type { ColDef, ICellRendererParams, CellValueChangedEvent } from "ag-grid-community";
import { AgGridReact } from "ag-grid-react";
import type { Table as TableModel } from "@kassierer/shared/model";
import { Tabs, type Tab } from "@/admin/ui/Tabs";

import { ModuleRegistry, AllCommunityModule } from "ag-grid-community";

import "ag-grid-community/styles/ag-grid.css";
import "ag-grid-community/styles/ag-theme-quartz.css";

ModuleRegistry.registerModules([AllCommunityModule]);

// Temporary table type for local state
type TempTable = {
  id: string;
  name: string;
  isTemporary: true;
};

// Combined type for table data
type TableData = (TableModel & { isTemporary?: false }) | TempTable;

// Update type for temporary tables
type TempTableUpdate = Partial<Omit<TempTable, "id" | "isTemporary">>;

// Action cell renderer component
const ActionCellRenderer = ({
  data,
  onDelete,
  onDeleteTemp,
}: {
  data: TableData;
  onDelete: (table: TableModel) => void;
  onDeleteTemp: (id: string) => void;
}) => {
  const isTemporary = "isTemporary" in data && data.isTemporary;

  return (
    <div className="flex gap-2">
      <Button
        label="Löschen"
        icon="pi pi-trash"
        severity="danger"
        size="small"
        onClick={() => {
          if (isTemporary) {
            onDeleteTemp(data.id);
          } else {
            onDelete(data as TableModel);
          }
        }}
      />
    </div>
  );
};

function AdminTablesPage() {
  const tables = useTables((state) => state.tables);
  const deleteTable = useTables((state) => state.deleteTable);
  const createTable = useTables((state) => state.createTable);

  const [activeTab, setActiveTab] = useState("tables");

  // Local state for temporary tables
  const [tempTables, setTempTables] = useState<TempTable[]>([]);

  // Combine real and temporary tables for display
  const allTables: TableData[] = [...tempTables, ...tables];

  // Helper functions
  const addTempTable = useCallback(() => {
    const newTempTable: TempTable = {
      id: crypto.randomUUID(),
      name: "",
      isTemporary: true,
    };
    setTempTables((prev) => [newTempTable, ...prev]);
  }, []);

  const updateTempTable = useCallback(
    (id: string, updates: TempTableUpdate) => {
      setTempTables((prev) =>
        prev.map((table) =>
          table.id === id ? { ...table, ...updates } : table,
        ),
      );
    },
    [],
  );

  const saveTempTable = useCallback(
    (tempTable: TempTable) => {
      if (tempTable.name.trim()) {
        createTable({
          name: tempTable.name.trim(),
        });
        setTempTables((prev) => prev.filter((t) => t.id !== tempTable.id));
      }
    },
    [createTable],
  );

  const deleteTempTable = useCallback((id: string) => {
    setTempTables((prev) => prev.filter((t) => t.id !== id));
  }, []);

  // Handle cell value changes
  const onCellValueChanged = useCallback(
    (event: CellValueChangedEvent<TableData>) => {
      const { data, colDef, newValue } = event;
      const isTemporary = "isTemporary" in data && data.isTemporary;

      if (!isTemporary) {
        // Handle real table updates - note: this would need a setTable function in useTables
        // For now, we'll skip this as tables might not support inline editing of real tables
        return;
      }

      if (isTemporary) {
        const field = colDef.field;
        const updates: TempTableUpdate = {};

        if (field === "name") {
          updates.name = newValue;
        }

        updateTempTable(data.id, updates);

        // Check if table should be saved
        const updatedTable = tempTables.find((t) => t.id === data.id);
        if (updatedTable) {
          const finalTable = { ...updatedTable, ...updates };
          if (finalTable.name.trim()) {
            saveTempTable(finalTable);
          }
        }
      }
    },
    [updateTempTable, tempTables, saveTempTable],
  );

  const tabs: Tab[] = [
    {
      name: "Tables",
      current: activeTab === "tables",
      onClick: () => setActiveTab("tables"),
    },
    {
      name: "Layout",
      current: activeTab === "layout",
      onClick: () => setActiveTab("layout"),
    },
  ];

  const columnDefs: ColDef<TableData>[] = [
    {
      headerName: "Status",
      field: "isTemporary",
      width: 100,
      valueFormatter: (params) => {
        const isTemporary = params?.data?.isTemporary;
        return isTemporary ? "Neu" : "OK";
      },
      sortable: false,
      filter: false,
      editable: false,
    },
    {
      headerName: "Name",
      field: "name",
      flex: 1,
      editable: true,
      sortable: false,
      filter: true,
    },
    {
      headerName: "Aktionen",
      width: 150,
      cellRenderer: (params: ICellRendererParams<TableData>) => (
        <ActionCellRenderer
          data={params.data!}
          onDelete={({ id }) => deleteTable(id)}
          onDeleteTemp={deleteTempTable}
        />
      ),
      sortable: false,
      filter: false,
      editable: false,
    },
  ];

  const renderTabContent = () => {
    switch (activeTab) {
      case "tables":
        return (
          <div className="space-y-6">
            <div className="px-4 py-5 sm:px-6">
              {/* Content goes here */}
              {/* We use less vertical padding on card headers on desktop than on body sections */}

              <div className="sm:flex sm:items-center">
                <div className="sm:flex-auto">
                  <h1 className="text-base font-semibold text-gray-900">
                    Tische
                  </h1>
                </div>
                <div className="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
                  <button
                    type="button"
                    className="block rounded-md cursor-pointer bg-indigo-600 px-3 py-2 text-center text-sm font-semibold text-white shadow-xs hover:bg-indigo-500 focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
                    onClick={addTempTable}
                  >
                    Erstellen
                  </button>
                </div>
              </div>
            </div>

            {/* Tables Grid */}
            <div className="ag-theme-quartz">
              <AgGridReact
                rowData={allTables}
                columnDefs={columnDefs}
                defaultColDef={{
                  resizable: true,
                  sortable: true,
                  filter: true,
                }}
                pagination={true}
                paginationPageSize={10}
                domLayout="autoHeight"
                getRowId={(params) => params.data.id}
                onCellValueChanged={onCellValueChanged}
              />
            </div>
          </div>
        );
      case "layout":
        return (
          <div className="text-center py-12">
            <p className="text-gray-500">Layout configuration coming soon...</p>
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <div className="divide-y divide-gray-200 h-full overflow-hidden rounded-lg bg-white shadow-sm">
      <div className="px-4 py-5 sm:px-6">
        <div className="sm:flex sm:items-center">
          <div className="sm:flex-auto">
            <h1 className="text-base font-semibold text-gray-900">Tische</h1>
          </div>
        </div>
      </div>

      <div className="px-4 py-5 sm:px-6">
        <Tabs
          tabs={tabs}
          onTabChange={(tab) => console.log("Tab changed to:", tab.name)}
          aria-label="Tables navigation"
        />
      </div>

      <div className="px-4 py-5 sm:px-6">{renderTabContent()}</div>
    </div>
  );
}

export default AdminTablesPage;
