import { useState } from "react";
import { useTables } from "@/data/useTables";
import { Card } from "primereact/card";
import { InputText } from "primereact/inputtext";
import { FloatLabel } from "primereact/floatlabel";
import { Button } from "primereact/button";
import type { ColDef, ICellRendererParams } from "ag-grid-community";
import { AgGridReact } from "ag-grid-react";
import type { Table as TableModel } from "@kassierer/shared/model";
import { Tabs, type Tab } from "@/admin/ui/Tabs";

import { ModuleRegistry, AllCommunityModule } from "ag-grid-community";

import "ag-grid-community/styles/ag-grid.css";
import "ag-grid-community/styles/ag-theme-quartz.css";

ModuleRegistry.registerModules([AllCommunityModule]);

// Action cell renderer component
const ActionCellRenderer = ({
  data,
  onDelete,
}: {
  data: TableModel;
  onDelete: (table: TableModel) => void;
}) => {
  return (
    <div className="flex gap-2">
      <Button
        label="Löschen"
        icon="pi pi-trash"
        severity="danger"
        size="small"
        onClick={() => onDelete(data)}
      />
    </div>
  );
};

function AdminTablesPage() {
  const tables = useTables((state) => state.tables);
  const deleteTable = useTables((state) => state.deleteTable);
  const createTable = useTables((state) => state.createTable);

  const [activeTab, setActiveTab] = useState("tables");

  const tabs: Tab[] = [
    {
      name: "Tables",
      current: activeTab === "tables",
      onClick: () => setActiveTab("tables"),
    },
    {
      name: "Layout",
      current: activeTab === "layout",
      onClick: () => setActiveTab("layout"),
    },
  ];

  const columnDefs: ColDef<TableModel>[] = [
    {
      headerName: "Name",
      field: "name",
      flex: 1,
      sortable: true,
      filter: true,
      editable: false,
    },
    {
      headerName: "Aktionen",
      width: 150,
      cellRenderer: (params: ICellRendererParams<TableModel>) => (
        <ActionCellRenderer data={params.data!} onDelete={deleteTable} />
      ),
      sortable: false,
      filter: false,
    },
  ];

  const renderTabContent = () => {
    switch (activeTab) {
      case "tables":
        return (
          <div className="space-y-6">
            <div className="px-4 py-5 sm:px-6">
              {/* Content goes here */}
              {/* We use less vertical padding on card headers on desktop than on body sections */}

              <div className="sm:flex sm:items-center">
                <div className="sm:flex-auto">
                  <h1 className="text-base font-semibold text-gray-900">
                    Produkte
                  </h1>
                </div>
                <div className="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
                  <button
                    type="button"
                    className="block rounded-md cursor-pointer bg-indigo-600 px-3 py-2 text-center text-sm font-semibold text-white shadow-xs hover:bg-indigo-500 focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
                    onClick={createTable}
                  >
                    Erstellen
                  </button>
                </div>
              </div>
            </div>

            {/* Tables Grid */}
            <div className="ag-theme-quartz">
              <AgGridReact
                rowData={tables}
                columnDefs={columnDefs}
                defaultColDef={{
                  resizable: true,
                  sortable: true,
                  filter: true,
                }}
                pagination={true}
                paginationPageSize={10}
                domLayout="autoHeight"
                getRowId={(params) => params.data.id}
              />
            </div>
          </div>
        );
      case "layout":
        return (
          <div className="text-center py-12">
            <p className="text-gray-500">Layout configuration coming soon...</p>
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <div className="divide-y divide-gray-200 h-full overflow-hidden rounded-lg bg-white shadow-sm">
      <div className="px-4 py-5 sm:px-6">
        <div className="sm:flex sm:items-center">
          <div className="sm:flex-auto">
            <h1 className="text-base font-semibold text-gray-900">Tische</h1>
          </div>
        </div>
      </div>

      <div className="px-4 py-5 sm:px-6">
        <Tabs
          tabs={tabs}
          onTabChange={(tab) => console.log("Tab changed to:", tab.name)}
          aria-label="Tables navigation"
        />
      </div>

      <div className="px-4 py-5 sm:px-6">{renderTabContent()}</div>
    </div>
  );
}

export default AdminTablesPage;
