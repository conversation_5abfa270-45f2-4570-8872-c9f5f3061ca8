import { createBrowserRouter, Outlet } from "react-router-dom";
import AdminProductCategories from "./admin/Categories/AdminCategoriesPage.tsx";
import AdminProducts from "./admin/Products/Page";
import AdminLayout from "./admin/layout";
import Kassierer from "./kassierer/kassierer";
import AdminOrderDetail from "./admin/order-detail";
import AdminTablesPage from "./admin/Tables/Page";
import AdminOrderPage from "@/admin/Order/Page";
import LoginPage from "@/user/LoginPage";
import RootPage from "@/RootPage";
import { AuthGuard } from "@/user/AuthGuard";
import RegisterPage from "./user/RegisterPage.tsx";

export const router = createBrowserRouter(
  [
    {
      path: "/",
      element: <RootPage />,
    },
    {
      path: "login",
      element: <LoginPage />,
    },
    {
      id: "register",
      path: "register",
      element: <RegisterPage />,
    },
    {
      path: "app",
      element: <AuthGuard><Outlet /></AuthGuard>,
      children: [
        {
          path: "kassierer",
          element: <KassiererTables />,
        },
        {
          path: "kassierer",
          element: <Kassierer />,
        },
      ],
    },
    {
      path: "admin",
      element: (
        <AuthGuard>
          <AdminLayout />
        </AuthGuard>
      ),
      children: [
        {
          path: "tables",
          element: <AdminTablesPage />,
        },
        {
          path: "product-categories",
          element: <AdminProductCategories />,
        },
        {
          path: "products",
          element: <AdminProducts />,
        },
        {
          path: "orders",
          element: <AdminOrderPage />,
        },
        {
          path: "orders/:orderId",
          element: <AdminOrderDetail />,
        },
      ],
    },
  ],
  {
    basename: import.meta.env["VITE_FRONTEND_BASE_PATH"],
  },
);
