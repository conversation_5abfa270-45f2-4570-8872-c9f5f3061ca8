.wrapper {
    display: flex;
    flex-direction: column;
}

.window {
    display: grid;
    grid-template-areas:
        "uppersidebar upper"
        "lower lower"
        "lowersidebar bottom";
    grid-template-columns: 3rem auto;
    grid-template-rows: 1.5fr 1fr 2rem;
    column-gap: 1rem;
    height: 100%;
    min-height: 0;
    min-width: 0;
}

.area_sidebar {
    grid-area: uppersidebar;
    overflow: hidden;
}

.area_uppersidebar {
    grid-area: uppersidebar;
    overflow: hidden;
}

.area_lowersidebar {
    grid-area: lowersidebar;
    overflow: hidden;
}

.area_upper {
    grid-area: upper;
    overflow: hidden;
}

.area_lower {
    grid-area: lower;
    overflow: hidden;
}

.area_bottom {
    grid-area: bottom;
}

@media screen and (min-width: 64rem) {
    .window {
    grid-template-areas:
        "uppersidebar upper"
        "lowersidebar lower"
        "lowersidebar bottom";
    grid-template-columns: 3rem auto;
    grid-template-rows: 1.5fr 1fr 2rem;
    }
}

@media screen and (min-width: 80rem) {
    .window {
    grid-template-areas:
        "uppersidebar upper lower"
        "lowersidebar upper lower";
    grid-template-columns: 3rem 1fr 1fr;
    grid-template-rows: 1fr 1fr;
    }
}