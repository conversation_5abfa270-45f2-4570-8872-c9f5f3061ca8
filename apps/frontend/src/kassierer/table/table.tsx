import { useTables } from "@/data/useTables";
import { useOrders } from "@/data/useOrders";
import { useUserSettings } from "@/data/useUserSettings";
import { useEffect, useMemo } from "react";
import { useNavigate } from "react-router-dom";
import layout from "@/kassierer/ui/kassierer-layout.module.css";
import type { Table, Order } from "@kassierer/shared/model";

function KassiererTables() {
  const navigate = useNavigate();

  // Settings
  const darkMode = useUserSettings(state => state.darkMode);
  useEffect(() => {
    if (darkMode) {
      document.documentElement.classList.add("dark");
    } else {
      document.documentElement.classList.remove("dark");
    }
  }, [darkMode]);

  // Data
  const tables = useTables((state) => state.tables);
  const orders = useOrders((state) => state.orders);

  // Calculate which tables have open (unpaid) orders
  const tablesWithOpenOrders = useMemo(() => {
    const tableIds = new Set<string>();

    orders.forEach((order: Order) => {
      // Check if this order has any unpaid items
      const hasUnpaidItems = order.content.some(contentItem =>
        contentItem.items.some(item => !item.payed)
      );

      if (hasUnpaidItems && order.tableId) {
        tableIds.add(order.tableId);
      }
    });

    return tableIds;
  }, [orders]);

  const handleTableClick = (table: Table) => {
    navigate(`/app/kassierer/${table.id}/order`);
  };

  return (
    <div
      className={
        "fixed top-0 left-0 w-full h-full dark:bg-gray-900 dark:text-white dark " +
        layout.wrapper
      }
    >
      <div className={layout.window}>
        <div className={layout.area_sidebar}>
          {/* Empty sidebar for consistency with order view */}
        </div>

        <div className={layout.area_upper}>
          <div className="h-full overflow-auto p-4">
            <div className="mb-6">
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                Tisch auswählen
              </h1>
              <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
                Wählen Sie einen Tisch aus, um eine Bestellung aufzunehmen.
              </p>
            </div>

            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4">
              {tables.map((table) => {
                const hasOpenOrders = tablesWithOpenOrders.has(table.id);

                return (
                  <button
                    key={table.id}
                    onClick={() => handleTableClick(table)}
                    className={`
                      relative aspect-square rounded-lg border-2 p-4 text-center font-semibold transition-all duration-200 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-900
                      ${hasOpenOrders
                        ? 'border-red-500 bg-red-50 text-red-700 hover:bg-red-100 dark:border-red-400 dark:bg-red-900/20 dark:text-red-300 dark:hover:bg-red-900/30'
                        : 'border-gray-300 bg-white text-gray-700 hover:bg-gray-50 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-300 dark:hover:bg-gray-700'
                      }
                    `}
                  >
                    <div className="flex flex-col items-center justify-center h-full">
                      <div className="text-lg font-bold mb-1">
                        {table.name}
                      </div>
                      {hasOpenOrders && (
                        <div className="absolute top-2 right-2">
                          <div className="w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
                        </div>
                      )}
                      {hasOpenOrders && (
                        <div className="text-xs opacity-75">
                          Offene Bestellung
                        </div>
                      )}
                    </div>
                  </button>
                );
              })}
            </div>

            {tables.length === 0 && (
              <div className="text-center py-12">
                <div className="text-gray-400 dark:text-gray-500">
                  <svg className="mx-auto h-12 w-12 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                  </svg>
                  <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                    Keine Tische verfügbar
                  </h3>
                  <p className="text-gray-500 dark:text-gray-400">
                    Es wurden noch keine Tische erstellt. Erstellen Sie Tische im Admin-Bereich.
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>

        <div className={layout.area_lower}>
          <div className="h-full overflow-auto p-4">
            <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
              <h3 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                Legende
              </h3>
              <div className="space-y-2 text-xs">
                <div className="flex items-center">
                  <div className="w-4 h-4 border-2 border-gray-300 bg-white dark:border-gray-600 dark:bg-gray-800 rounded mr-2"></div>
                  <span className="text-gray-600 dark:text-gray-400">Verfügbar</span>
                </div>
                <div className="flex items-center">
                  <div className="w-4 h-4 border-2 border-red-500 bg-red-50 dark:border-red-400 dark:bg-red-900/20 rounded mr-2 relative">
                    <div className="absolute top-0 right-0 w-1.5 h-1.5 bg-red-500 rounded-full"></div>
                  </div>
                  <span className="text-gray-600 dark:text-gray-400">Offene Bestellung</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default KassiererTables;
