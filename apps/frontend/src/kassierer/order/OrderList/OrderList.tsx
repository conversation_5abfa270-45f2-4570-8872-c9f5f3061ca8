import { OrderListItem } from "./OderListItem";
import type { Order, Product } from "@kassierer/shared/model";
import React, { useMemo } from "react";

type Props = {
  products: Product[],
  orderContent: Order["content"];
  onIncrease: (product: Product) => void;
  onDecrease: (product: Product) => void;
  onDelete: (product: Product) => void;
};

export const KassiererOrderList: React.FC<Props> = ({
  products,
  orderContent,
  onIncrease,
  onDecrease,
  onDelete,
}) => {

  const productsMap = useMemo(() => products?.reduce<Record<Product["id"], Product>>(
    (pMap, product) => {
      pMap[product.id] = product;
      return pMap;
    },
    {},
  ), [products]);

  return (
    <table className="flex flex-col gap-1 md:pt-0 p-2 overflow-y-auto overflow-x-hidden h-full">
      {orderContent.map((item) => {
        return (
          <OrderListItem
            key={`kassierer-item-${item.productId}`}
            product={productsMap[item.productId]}
            quantity={item.items.length}
            onIncrease={onIncrease}
            onDecrease={onDecrease}
            onDelete={onDelete}
          />
        );
      })}
    </table>
  );
};
