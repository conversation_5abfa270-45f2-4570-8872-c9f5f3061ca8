import type { Product, ProductCategory } from "@kassierer/shared/model";
import React, { useMemo } from "react";
import styles from "./ProductList.module.css";
import { classes } from "@/utils/classes";

const generateProductListItemId = (id: string) => {
  return `kassierer-product-list-item-${id}`;
};

type Props = {
  products: Product[];
  categories: ProductCategory[];
  onIncrease: (product: Product) => void;
};

export const KassiererProductList: React.FC<Props> = ({
  products,
  categories,
  onIncrease,
}) => {
  const productsByCategory = useMemo(() => {
    const productsMap = new Map<ProductCategory, Product[]>();
    categories.forEach((category) => {
      const productsByCat = products.filter(
        (p) => p.categoryId === category.id,
      );
      productsMap.set(category, productsByCat);
    });
    return productsMap;
  }, [categories, products]);

  //#region KASSIERER PRODUCT
  //   const { increaseKassiererProduct } = useSetKassiererItem();
  function increaseKassiererProduct(product: Product) {
    onIncrease(product);
  }
  //#endregion KASSIERER PRODUCT

  return (
    <div className="flex max-h-full">
      <div
        id="kassierer-product-list-scrollspy"
        className="flex flex-col w-11 px-2 gap-2 overflow-y-auto"
      >
        {categories.map((category) => {
          return (
            <div
              key={`kassierer-product-list-category-${category.id}`}
              className={classes(styles["product-list-category-button"])}
              onClick={() => {
                document
                  .getElementById(generateProductListItemId(category.id))
                  ?.scrollIntoView({ behavior: "smooth" });
              }}
            >
              <span className="p-2 border border-solid border-gray-400 dark:bg-gray-600 rounded-xs text-lg">
                {category.name}
              </span>
            </div>
          );
        })}
      </div>
      <div className="flex flex-col flex-1 overflow-y-auto overflow-x-hidden pl-2">
        {categories.map((category) => {
          const productsByCat = productsByCategory.get(category);
          if (productsByCat?.length) {
            return (
              <div
                key={generateProductListItemId(category.id)}
                id={generateProductListItemId(category.id)}
                className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2 w-full mb-3 last:mb-0"
              >
                {productsByCat.map((product) => (
                  <button
                    key={`kassierer-product-list-${product.id}`}
                    className="flex items-center rounded-md border-none justify-center bg-black dark:bg-white px-2 py-4 text-white dark:text-black"
                    onClick={() => increaseKassiererProduct(product)}
                  >
                    {product.name}
                  </button>
                ))}
              </div>
            );
          } else {
            return null;
          }
        })}
      </div>
    </div>
  );
};
